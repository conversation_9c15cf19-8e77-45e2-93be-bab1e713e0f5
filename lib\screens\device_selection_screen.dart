import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/device_model.dart';

/// 设备和摄像头选择界面
class DeviceSelectionScreen extends StatefulWidget {
  final DeviceManager deviceManager;
  final Function(CameraChannelInfo) onChannelSelected;
  final VoidCallback onClose;

  const DeviceSelectionScreen({
    Key? key,
    required this.deviceManager,
    required this.onChannelSelected,
    required this.onClose,
  }) : super(key: key);

  @override
  State<DeviceSelectionScreen> createState() => _DeviceSelectionScreenState();
}

class _DeviceSelectionScreenState extends State<DeviceSelectionScreen> {
  int _selectedDeviceIndex = 0;
  int _selectedChannelIndex = 0;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      onKeyEvent: _handleKeyEvent,
      child: Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: _buildDeviceList(),
                  ),
                  const SizedBox(width: 24),
                  Expanded(
                    flex: 2,
                    child: _buildChannelGrid(),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            _buildBottomBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Text(
          '选择摄像头',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        GestureDetector(
          onTap: widget.onClose,
          child: const Icon(
            Icons.close,
            color: Colors.white,
            size: 32,
          ),
        ),
      ],
    );
  }

  Widget _buildDeviceList() {
    final devices = widget.deviceManager.connectedDevices;
    
    if (devices.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.device_hub,
                color: Colors.grey,
                size: 48,
              ),
              SizedBox(height: 16),
              Text(
                '未发现设备',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              '录像机列表',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: devices.length,
              itemBuilder: (context, index) {
                return _buildDeviceItem(devices[index], index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceItem(DeviceModel device, int index) {
    final isSelected = index == _selectedDeviceIndex;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedDeviceIndex = index;
          _selectedChannelIndex = 0;
        });
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.withOpacity(0.3) : Colors.transparent,
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              device.device.name,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey[300],
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              device.device.ip,
              style: TextStyle(
                color: isSelected ? Colors.white70 : Colors.grey,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${device.getEnabledChannels().length} 个通道',
              style: TextStyle(
                color: isSelected ? Colors.white70 : Colors.grey,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChannelGrid() {
    final devices = widget.deviceManager.connectedDevices;
    
    if (devices.isEmpty || _selectedDeviceIndex >= devices.length) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Text(
            '请先选择录像机',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    final selectedDevice = devices[_selectedDeviceIndex];
    final channels = selectedDevice.getEnabledChannels();

    if (channels.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Text(
            '该设备没有可用通道',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              '摄像头通道 (${selectedDevice.device.name})',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 2.5,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: channels.length,
              itemBuilder: (context, index) {
                return _buildChannelItem(
                  CameraChannelInfo(
                    device: selectedDevice,
                    channel: channels[index],
                  ),
                  index,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChannelItem(CameraChannelInfo channelInfo, int index) {
    final isSelected = index == _selectedChannelIndex;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedChannelIndex = index;
        });
      },
      onDoubleTap: () {
        widget.onChannelSelected(channelInfo);
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.withOpacity(0.3) : Colors.black.withOpacity(0.3),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              children: [
                Icon(
                  Icons.videocam,
                  color: isSelected ? Colors.white : Colors.grey,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    channelInfo.channel.name,
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.grey[300],
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              '通道 ${channelInfo.channel.id}',
              style: TextStyle(
                color: isSelected ? Colors.white70 : Colors.grey,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          '使用方向键选择，回车键确认，ESC键退出',
          style: TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is! KeyDownEvent) {
      return KeyEventResult.ignored;
    }

    final devices = widget.deviceManager.connectedDevices;
    if (devices.isEmpty) {
      return KeyEventResult.ignored;
    }

    switch (event.logicalKey) {
      case LogicalKeyboardKey.arrowUp:
        _moveSelection(-1, 0);
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.arrowDown:
        _moveSelection(1, 0);
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.arrowLeft:
        _moveSelection(0, -1);
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.arrowRight:
        _moveSelection(0, 1);
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.enter:
      case LogicalKeyboardKey.select:
        _selectCurrentChannel();
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.escape:
      case LogicalKeyboardKey.goBack:
        widget.onClose();
        return KeyEventResult.handled;
        
      default:
        return KeyEventResult.ignored;
    }
  }

  void _moveSelection(int deviceDelta, int channelDelta) {
    setState(() {
      if (deviceDelta != 0) {
        final devices = widget.deviceManager.connectedDevices;
        _selectedDeviceIndex = (_selectedDeviceIndex + deviceDelta).clamp(0, devices.length - 1);
        _selectedChannelIndex = 0; // 重置通道选择
      }
      
      if (channelDelta != 0) {
        final devices = widget.deviceManager.connectedDevices;
        if (_selectedDeviceIndex < devices.length) {
          final channels = devices[_selectedDeviceIndex].getEnabledChannels();
          _selectedChannelIndex = (_selectedChannelIndex + channelDelta).clamp(0, channels.length - 1);
        }
      }
    });
  }

  void _selectCurrentChannel() {
    final devices = widget.deviceManager.connectedDevices;
    if (_selectedDeviceIndex < devices.length) {
      final selectedDevice = devices[_selectedDeviceIndex];
      final channels = selectedDevice.getEnabledChannels();
      if (_selectedChannelIndex < channels.length) {
        final channelInfo = CameraChannelInfo(
          device: selectedDevice,
          channel: channels[_selectedChannelIndex],
        );
        widget.onChannelSelected(channelInfo);
      }
    }
  }
}
