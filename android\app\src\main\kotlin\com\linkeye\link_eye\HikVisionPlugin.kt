package com.linkeye.link_eye

import android.content.Context
import android.util.Log
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.net.DatagramPacket
import java.net.DatagramSocket
import java.net.InetAddress
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import org.json.JSONObject
import org.json.JSONArray

/**
 * 海康威视设备发现和视频播放插件
 */
class HikVisionPlugin : FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context
    private val executor: ScheduledExecutorService = Executors.newScheduledThreadPool(4)
    
    companion object {
        private const val TAG = "HikVisionPlugin"
        private const val CHANNEL_NAME = "com.linkeye.link_eye/hikvision"
        
        // 海康威视设备发现相关常量
        private const val DISCOVERY_PORT = 37020
        private const val DISCOVERY_TIMEOUT = 5000
        private const val HTTP_PORT = 80
    }

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, CHANNEL_NAME)
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
        executor.shutdown()
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "discoverDevices" -> {
                discoverHikVisionDevices(result)
            }
            "getChannels" -> {
                val ip = call.argument<String>("ip")
                val username = call.argument<String>("username") ?: "admin"
                val password = call.argument<String>("password") ?: "12345"
                if (ip != null) {
                    getDeviceChannels(ip, username, password, result)
                } else {
                    result.error("INVALID_ARGUMENT", "IP address is required", null)
                }
            }
            "startPreview" -> {
                val ip = call.argument<String>("ip")
                val channel = call.argument<Int>("channel")
                val username = call.argument<String>("username") ?: "admin"
                val password = call.argument<String>("password") ?: "12345"
                if (ip != null && channel != null) {
                    startVideoPreview(ip, channel, username, password, result)
                } else {
                    result.error("INVALID_ARGUMENT", "IP and channel are required", null)
                }
            }
            "stopPreview" -> {
                val playHandle = call.argument<Int>("playHandle")
                if (playHandle != null) {
                    stopVideoPreview(playHandle, result)
                } else {
                    result.error("INVALID_ARGUMENT", "Play handle is required", null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    /**
     * 发现海康威视设备
     */
    private fun discoverHikVisionDevices(result: Result) {
        executor.execute {
            try {
                val devices = mutableListOf<Map<String, Any>>()
                val socket = DatagramSocket()
                socket.soTimeout = DISCOVERY_TIMEOUT
                
                // 构造设备发现报文
                val discoveryMessage = buildDiscoveryMessage()
                val broadcastAddress = InetAddress.getByName("***************")
                val packet = DatagramPacket(
                    discoveryMessage.toByteArray(),
                    discoveryMessage.length,
                    broadcastAddress,
                    DISCOVERY_PORT
                )
                
                Log.d(TAG, "Sending discovery broadcast...")
                socket.send(packet)
                
                // 接收响应
                val buffer = ByteArray(1024)
                val startTime = System.currentTimeMillis()
                
                while (System.currentTimeMillis() - startTime < DISCOVERY_TIMEOUT) {
                    try {
                        val responsePacket = DatagramPacket(buffer, buffer.size)
                        socket.receive(responsePacket)
                        
                        val response = String(responsePacket.data, 0, responsePacket.length)
                        Log.d(TAG, "Received response: $response")
                        
                        val deviceInfo = parseDeviceResponse(response, responsePacket.address.hostAddress)
                        if (deviceInfo != null) {
                            devices.add(deviceInfo)
                        }
                    } catch (e: Exception) {
                        // 超时或其他异常，继续等待其他设备响应
                        break
                    }
                }
                
                socket.close()
                Log.d(TAG, "Discovery completed, found ${devices.size} devices")
                result.success(devices)
                
            } catch (e: Exception) {
                Log.e(TAG, "Device discovery failed", e)
                result.error("DISCOVERY_FAILED", e.message, null)
            }
        }
    }

    /**
     * 构造设备发现报文
     */
    private fun buildDiscoveryMessage(): String {
        return """<?xml version="1.0" encoding="UTF-8"?>
<Probe>
<Uuid>*************-4444-4444-************</Uuid>
<Types>inquiry</Types>
</Probe>"""
    }

    /**
     * 解析设备响应
     */
    private fun parseDeviceResponse(response: String, ip: String): Map<String, Any>? {
        return try {
            // 简化的解析，实际应该解析XML
            if (response.contains("ProbeMatch") || response.contains("Device")) {
                mapOf(
                    "ip" to ip,
                    "name" to "HikVision Device",
                    "model" to "Unknown",
                    "serialNumber" to "Unknown",
                    "httpPort" to HTTP_PORT
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse device response", e)
            null
        }
    }

    /**
     * 获取设备通道信息
     */
    private fun getDeviceChannels(ip: String, username: String, password: String, result: Result) {
        executor.execute {
            try {
                // 模拟获取通道信息，实际应该通过HTTP API获取
                val channels = mutableListOf<Map<String, Any>>()
                
                // 假设设备有4个通道
                for (i in 1..4) {
                    channels.add(mapOf(
                        "id" to i,
                        "name" to "Camera $i",
                        "enabled" to true,
                        "rtspUrl" to "rtsp://$username:$password@$ip:554/Streaming/Channels/${i}01"
                    ))
                }
                
                Log.d(TAG, "Retrieved ${channels.size} channels for device $ip")
                result.success(channels)
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get device channels", e)
                result.error("GET_CHANNELS_FAILED", e.message, null)
            }
        }
    }

    /**
     * 开始视频预览
     */
    private fun startVideoPreview(ip: String, channel: Int, username: String, password: String, result: Result) {
        executor.execute {
            try {
                // 构造RTSP URL
                val rtspUrl = "rtsp://$username:$password@$ip:554/Streaming/Channels/${channel}01"
                
                Log.d(TAG, "Starting video preview: $rtspUrl")
                
                // 返回模拟的播放句柄和RTSP URL
                val playInfo = mapOf(
                    "playHandle" to System.currentTimeMillis().toInt(),
                    "rtspUrl" to rtspUrl,
                    "status" to "playing"
                )
                
                result.success(playInfo)
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start video preview", e)
                result.error("START_PREVIEW_FAILED", e.message, null)
            }
        }
    }

    /**
     * 停止视频预览
     */
    private fun stopVideoPreview(playHandle: Int, result: Result) {
        executor.execute {
            try {
                Log.d(TAG, "Stopping video preview: $playHandle")
                
                // 模拟停止播放
                val stopInfo = mapOf(
                    "playHandle" to playHandle,
                    "status" to "stopped"
                )
                
                result.success(stopInfo)
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to stop video preview", e)
                result.error("STOP_PREVIEW_FAILED", e.message, null)
            }
        }
    }
}
