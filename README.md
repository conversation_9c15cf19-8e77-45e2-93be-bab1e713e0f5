# link_eye

A new Flutter project.

## Getting Started

# LinkEye 监控 - 海康威视多路视频播放应用

一个专为Android TV设计的Flutter应用，用于连接海康威视录像机并同时播放多路视频监控画面。

## 功能特性

### 🎥 多路视频播放
- 支持2路和4路视频同时播放
- 自适应Android TV屏幕布局
- 实时视频流播放

### 🔍 设备自动发现
- 自动发现网络中的海康威视录像机
- 支持手动添加设备
- 实时显示设备连接状态

### 📺 Android TV优化
- 专为Android TV遥控器设计的交互界面
- 支持方向键导航和选择
- 优化的大屏幕显示效果

### 🎛️ 遥控器控制
- **方向键**: 在播放器之间导航选择
- **确认键**: 选择播放器或确认操作
- **菜单键**:
  - 短按：打开摄像头选择界面
  - 长按：打开音频控制界面
- **返回键**: 退出当前界面
- **数字键1**: 切换到2路布局
- **数字键2**: 切换到4路布局
- **频道键**: 快速切换摄像头

### 🔊 音频控制
- 全局静音/取消静音
- 音量调节
- 单独控制每路音频
- 长按菜单键快速访问音频设置

### 📱 摄像头管理
- 自动查询录像机所有摄像头通道
- 点击播放器选择不同摄像头
- 支持摄像头名称显示

## 技术架构

### 核心组件
- **HikVisionService**: 海康威视设备通信服务
- **DeviceManager**: 设备发现和管理
- **MultiVideoPlayerManager**: 多路视频播放管理
- **RemoteControlService**: 遥控器交互处理

### 主要依赖
- `video_player`: 视频播放
- `provider`: 状态管理
- `http` & `dio`: 网络请求
- `network_info_plus`: 网络信息获取

## 安装和使用

### 环境要求
- Flutter SDK 3.8.1+
- Android TV设备或模拟器
- 海康威视录像机（支持RTSP协议）

### 构建步骤
1. 克隆项目
```bash
git clone <repository-url>
cd link-eye
```

2. 安装依赖
```bash
flutter pub get
```

3. 构建Android TV版本
```bash
flutter build apk --target-platform android-arm64
```

4. 安装到Android TV设备
```bash
flutter install
```

### 使用说明

#### 首次使用
1. 启动应用后会自动搜索网络中的海康威视设备
2. 等待设备连接完成（顶部状态栏显示连接数量）
3. 使用遥控器选择播放器窗口
4. 按菜单键选择要播放的摄像头

#### 基本操作
- **选择播放器**: 使用方向键在不同播放器窗口间切换
- **播放视频**: 选中播放器后按菜单键，选择摄像头开始播放
- **切换布局**: 按数字键1切换到2路，按数字键2切换到4路
- **音频控制**: 长按菜单键打开音频控制面板

#### 高级功能
- **快速切换**: 使用频道上下键快速切换摄像头
- **批量操作**: 在底部控制栏可以停止所有播放或刷新设备
- **个性化设置**: 可以为每个播放器单独设置音频

## 网络配置

### 海康威视设备要求
- 设备需要在同一局域网内
- 启用RTSP服务
- 默认用户名: `admin`，密码: `12345`（可在代码中修改）

### 端口配置
- 设备发现端口: 37020 (UDP)
- HTTP管理端口: 80
- RTSP视频流端口: 554

## 故障排除

### 常见问题

**1. 无法发现设备**
- 检查网络连接
- 确认设备在同一网段
- 检查防火墙设置

**2. 视频无法播放**
- 验证RTSP URL是否正确
- 检查用户名密码
- 确认摄像头通道是否启用

**3. 遥控器无响应**
- 确保应用获得焦点
- 检查Android TV遥控器电池
- 重启应用

### 调试模式
应用内置了详细的日志系统，可以通过以下方式查看：
```bash
flutter logs
```

## 开发说明

### 项目结构
```
lib/
├── main.dart                 # 应用入口
├── models/                   # 数据模型
│   ├── device_model.dart     # 设备管理模型
│   └── video_player_model.dart # 视频播放模型
├── screens/                  # 界面
│   ├── main_screen.dart      # 主界面
│   └── device_selection_screen.dart # 设备选择界面
├── services/                 # 服务层
│   ├── hikvision_service.dart # 海康威视服务
│   └── remote_control_service.dart # 遥控器服务
└── widgets/                  # 组件
    ├── video_player_widget.dart # 视频播放组件
    └── audio_control_widget.dart # 音频控制组件
```

### 扩展开发
- 支持更多品牌的录像机
- 添加录像回放功能
- 实现云台控制
- 添加录像功能

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者
