import 'package:flutter/services.dart';
import 'package:logger/logger.dart';

/// 海康威视设备信息
class HikVisionDevice {
  final String ip;
  final String name;
  final String model;
  final String serialNumber;
  final int httpPort;

  HikVisionDevice({
    required this.ip,
    required this.name,
    required this.model,
    required this.serialNumber,
    required this.httpPort,
  });

  factory HikVisionDevice.fromMap(Map<String, dynamic> map) {
    return HikVisionDevice(
      ip: map['ip'] ?? '',
      name: map['name'] ?? 'Unknown Device',
      model: map['model'] ?? 'Unknown',
      serialNumber: map['serialNumber'] ?? 'Unknown',
      httpPort: map['httpPort'] ?? 80,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'ip': ip,
      'name': name,
      'model': model,
      'serialNumber': serialNumber,
      'httpPort': httpPort,
    };
  }

  @override
  String toString() {
    return 'HikVisionDevice{ip: $ip, name: $name, model: $model}';
  }
}

/// 摄像头通道信息
class CameraChannel {
  final int id;
  final String name;
  final bool enabled;
  final String rtspUrl;

  CameraChannel({
    required this.id,
    required this.name,
    required this.enabled,
    required this.rtspUrl,
  });

  factory CameraChannel.fromMap(Map<String, dynamic> map) {
    return CameraChannel(
      id: map['id'] ?? 0,
      name: map['name'] ?? 'Unknown Channel',
      enabled: map['enabled'] ?? false,
      rtspUrl: map['rtspUrl'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'enabled': enabled,
      'rtspUrl': rtspUrl,
    };
  }

  @override
  String toString() {
    return 'CameraChannel{id: $id, name: $name, enabled: $enabled}';
  }
}

/// 视频播放信息
class VideoPlayInfo {
  final int playHandle;
  final String rtspUrl;
  final String status;

  VideoPlayInfo({
    required this.playHandle,
    required this.rtspUrl,
    required this.status,
  });

  factory VideoPlayInfo.fromMap(Map<String, dynamic> map) {
    return VideoPlayInfo(
      playHandle: map['playHandle'] ?? 0,
      rtspUrl: map['rtspUrl'] ?? '',
      status: map['status'] ?? 'unknown',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'playHandle': playHandle,
      'rtspUrl': rtspUrl,
      'status': status,
    };
  }

  @override
  String toString() {
    return 'VideoPlayInfo{playHandle: $playHandle, status: $status}';
  }
}

/// 海康威视服务类
class HikVisionService {
  static const MethodChannel _channel = MethodChannel('com.linkeye.link_eye/hikvision');
  static final Logger _logger = Logger();

  /// 发现网络中的海康威视设备
  static Future<List<HikVisionDevice>> discoverDevices() async {
    try {
      _logger.i('开始发现海康威视设备...');
      
      final List<dynamic> result = await _channel.invokeMethod('discoverDevices');
      
      final devices = result
          .map((deviceMap) => HikVisionDevice.fromMap(Map<String, dynamic>.from(deviceMap)))
          .toList();
      
      _logger.i('发现 ${devices.length} 个设备: ${devices.map((d) => d.ip).join(', ')}');
      return devices;
      
    } catch (e) {
      _logger.e('设备发现失败: $e');
      return [];
    }
  }

  /// 获取设备的摄像头通道列表
  static Future<List<CameraChannel>> getDeviceChannels(
    String ip, {
    String username = 'admin',
    String password = '12345',
  }) async {
    try {
      _logger.i('获取设备 $ip 的通道信息...');
      
      final List<dynamic> result = await _channel.invokeMethod('getChannels', {
        'ip': ip,
        'username': username,
        'password': password,
      });
      
      final channels = result
          .map((channelMap) => CameraChannel.fromMap(Map<String, dynamic>.from(channelMap)))
          .toList();
      
      _logger.i('设备 $ip 有 ${channels.length} 个通道');
      return channels;
      
    } catch (e) {
      _logger.e('获取通道信息失败: $e');
      return [];
    }
  }

  /// 开始视频预览
  static Future<VideoPlayInfo?> startPreview(
    String ip,
    int channel, {
    String username = 'admin',
    String password = '12345',
  }) async {
    try {
      _logger.i('开始播放 $ip 通道 $channel 的视频...');
      
      final Map<String, dynamic> result = await _channel.invokeMethod('startPreview', {
        'ip': ip,
        'channel': channel,
        'username': username,
        'password': password,
      });
      
      final playInfo = VideoPlayInfo.fromMap(result);
      _logger.i('视频播放开始: $playInfo');
      return playInfo;
      
    } catch (e) {
      _logger.e('开始视频播放失败: $e');
      return null;
    }
  }

  /// 停止视频预览
  static Future<bool> stopPreview(int playHandle) async {
    try {
      _logger.i('停止播放句柄 $playHandle 的视频...');
      
      final Map<String, dynamic> result = await _channel.invokeMethod('stopPreview', {
        'playHandle': playHandle,
      });
      
      _logger.i('视频播放已停止: $result');
      return result['status'] == 'stopped';
      
    } catch (e) {
      _logger.e('停止视频播放失败: $e');
      return false;
    }
  }

  /// 测试设备连接
  static Future<bool> testConnection(
    String ip, {
    String username = 'admin',
    String password = '12345',
  }) async {
    try {
      final channels = await getDeviceChannels(ip, username: username, password: password);
      return channels.isNotEmpty;
    } catch (e) {
      _logger.e('测试连接失败: $e');
      return false;
    }
  }
}
